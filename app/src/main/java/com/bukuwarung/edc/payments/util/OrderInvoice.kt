package com.bukuwarung.edc.payments.util

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.databinding.LayoutOrderInvoiceViewBinding
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.util.*


/**
 * OrderInvoice is used to create invoice for payment, PPOB orders, Balance Check and Money Transfer using cards.
 * This uses a remote configs, invoice_data_block to manage header and footers
 * Data regarding PPOB or payment specific items will come from OrderInvoiceConst
 */
class OrderInvoice @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var orderResponse: OrderResponse? = null
    private var paymentType: String? = null
    private var cardReceiptResponse: CardReceiptResponse? = null
    private val binding =
        LayoutOrderInvoiceViewBinding.inflate(LayoutInflater.from(context), this, true)

    // 3rd item is for Payments & PPOB
    private val storeDetailDataItem =
        PaymentRemoteConfig.getInvoiceMetadata().data?.getOrNull(0)?.storeDetailData?.getOrNull(3)
    private val footerDataItem =
        PaymentRemoteConfig.getInvoiceMetadata().data?.getOrNull(0)?.footerData?.getOrNull(3)
    private val dottedLine: String = "--------------------------------"

    fun makeInvoice(
        orderResponse: OrderResponse?,
        paymentType: String?,
    ) {
        this.orderResponse = orderResponse
        this.paymentType = paymentType
        populateData()
    }

    fun makeCardInvoice(
        cardReceiptResponse: CardReceiptResponse?,
        pan: String,
        accountType: String,
        notes: String,
        cardType: Int = Constants.CARD_ENTRY_MODE_IC
    ) {
        this.cardReceiptResponse = cardReceiptResponse
        populateCardData(pan, accountType, notes, cardType)
    }

    private fun populateCardData(pan: String, accountType: String, notes: String, cardType:Int = Constants.CARD_ENTRY_MODE_IC) {
        populateStoreDetails(true)
        cardReceiptResponse?.let {
            OrderInvoiceUtils.addCardInvoiceDetailsToLayout(
                context = context,
                cardReceiptResponse = it,
                viewGroupToAddViews = binding.llInvoiceDetail,
                parent = this,
                pan = pan,
                accountType = accountType,
                notes = notes,
                cardType = cardType
            )
        }
        val pendingTransactionFooterVisibility =
            cardReceiptResponse?.status.equals(PaymentConst.TRANSACTION_PENDING).asVisibility()
        handleFooterDataVisibility(pendingTransactionFooterVisibility)
    }

    private fun populateData() {
        populateStoreDetails(false)
        orderResponse?.let {
            OrderInvoiceUtils.addInvoiceDetailToLayout(
                context = context,
                order = it,
                viewGroupToAddViews = binding.llInvoiceDetail,
                parent = this,
                paymentType = paymentType
            )
        }
        handleFooterDataVisibility()
    }

    /**
     * Set the store details section
     */
    private fun populateStoreDetails(isCardReceipt: Boolean) {
        with(binding.layoutStoreDetail) {
            ivLogo.visibility = isCardReceipt.asVisibility()
            ivLogo.setImageDrawable(
                ContextCompat.getDrawable(
                    context,
                    if (Utils.isAtmPro()) R.drawable.bersama_logo else R.drawable.buku_agent_print
                )
            )
            tvStoreName.text = Utils.getBusinessName()
            tvStoreName.visibility = Utils.getBusinessName().isNotBlank().asVisibility()
            tvStoreAddress.text = Utils.getBusinessAddress()
            tvStoreAddress.visibility = Utils.getBusinessAddress().isNotBlank().asVisibility()
            if (!isCardReceipt) {
                tvStorePhone.text = Utils.getPhoneNumber()
                tvStorePhone.visibility = Utils.getPhoneNumber().isNotBlank().asVisibility()
            }
        }
    }

    private fun handleFooterDataVisibility(pendingTransactionFooterVisibility: Int = View.GONE) {
        with(binding.clInvoiceFooter) {
            if (Utils.isAtmPro() || footerDataItem?.elements?.getOrNull(0)?.bukuAd?.visibility.isFalseOrNull) {
                hideView()
            } else {
                showView()
            }
        }
        binding.tvPendingTransactionFooter.hideView()
    }

    fun extractTextForPaxPrinting(): String {
        var customerReceipt = ""
        orderResponse?.let { order ->
            repeat(8) { customerReceipt += " " }
            customerReceipt += Utils.getBusinessName()
            customerReceipt += "\n"
            repeat(9) { customerReceipt += " " }
            customerReceipt += Utils.getPhoneNumber()
            customerReceipt += "\n\n"

            customerReceipt += "Tanggal"
            repeat(6) { customerReceipt += " " }
            customerReceipt += DateTimeUtils.getLocalStringFromUtc(order.createdAt, DateTimeUtils.DD_MMM_YYYY_HH_MM)
            customerReceipt += "\n"
            customerReceipt += "Kode Bayar"
            repeat(3) { customerReceipt += " " }
            customerReceipt += order.transactionId
            customerReceipt += "\n$dottedLine"
            customerReceipt += "\n\n"

            repeat(10) { customerReceipt += " " }
            customerReceipt += "Pengirim\n\n"

            repeat(6) { customerReceipt += " " }
            customerReceipt += "${order.payments?.getOrNull(0)?.paymentMethod?.code} - ${ Utils.getBusinessName()}"
            customerReceipt += "\n$dottedLine"
            customerReceipt += "\n\n"

            repeat(8) { customerReceipt += " " }
            customerReceipt += "Rekening Tujuan"

            customerReceipt += "\n\n"
            repeat(6) { customerReceipt += " " }
            customerReceipt += order.items?.get(0)?.beneficiary?.name
            customerReceipt += "\n\n"

            repeat(7) { customerReceipt += " " }
            customerReceipt += order.items?.get(0)?.beneficiary?.code
            customerReceipt += " - "
            customerReceipt += order.items?.get(0)?.beneficiary?.accountNumber
            customerReceipt += "\n\n"
            repeat(6) { customerReceipt += " " }

            customerReceipt += "Pembayaran Berhasil"

            if (order.agentFeeInfo?.amount != 0.0) {
                customerReceipt += "\n"
                customerReceipt += dottedLine
                customerReceipt += "\n"

                customerReceipt += "\n"
                customerReceipt += "Jumlah Pembayaran"
                repeat(2) { customerReceipt += " " }
                customerReceipt += Utils.formatAmount(order.items?.get(0)?.sellingPrice)
                customerReceipt += "\n"

                customerReceipt += "Biaya Layanan"
                repeat(6) { customerReceipt += " " }
                customerReceipt += Utils.formatAmount(order.agentFeeInfo?.amount)
            }

            customerReceipt += "\n"
            customerReceipt += dottedLine
            customerReceipt += "\n"

            val total = order.items?.get(0)?.sellingPrice!! + order.agentFeeInfo?.amount!!

            customerReceipt += "Total Bayar"
            repeat(8) { customerReceipt += " " }
            customerReceipt += Utils.formatAmount(total)
            customerReceipt += "\n"
        }

        customerReceipt += dottedLine
        customerReceipt += "\n"
        customerReceipt += binding.tvFooterMessage.text
        customerReceipt += "\n"
        repeat(5) { customerReceipt += " " }
        customerReceipt += binding.tvFooterLink.text
        customerReceipt += "\n$dottedLine"
        customerReceipt += "\n\n\n\n\n"
        return customerReceipt
    }

    fun extractTextForVerifonePrinting(): String {
        var customerReceipt = ""
        orderResponse?.let { order ->
            customerReceipt += "\n"
            customerReceipt += "normal"
            customerReceipt += "\n"
            customerReceipt += "center"
            customerReceipt += "\n"
            customerReceipt += Utils.getBusinessName()
            customerReceipt += "\n"
            customerReceipt += Utils.getPhoneNumber()
            customerReceipt += "\n"
            customerReceipt += "feedline2"
            customerReceipt += "\n"
            customerReceipt += "feedline2"
            customerReceipt += "\n"

            customerReceipt += "left"
            customerReceipt += "\n"
            customerReceipt += "Tanggal"
            repeat(4) { customerReceipt += " " }
            customerReceipt += DateTimeUtils.getLocalStringFromUtc(order.createdAt, DateTimeUtils.DD_MMM_YYYY_HH_MM)
            customerReceipt += "\n"
            customerReceipt += "Kode Bayar"
            repeat(1) { customerReceipt += " " }
            customerReceipt += order.transactionId
            customerReceipt += "\n"
            customerReceipt += "dotted_line"
            customerReceipt += "\n"
            customerReceipt += "feedline"
            customerReceipt += "\n"

            customerReceipt += "center"
            customerReceipt += "\n"

            customerReceipt += "Pengirim"
            customerReceipt += "\n"
            customerReceipt += "feedline"
            customerReceipt += "\n"
            customerReceipt += "${order.payments?.getOrNull(0)?.paymentMethod?.code} - ${ Utils.getBusinessName()}"
            customerReceipt += "\n"
            customerReceipt += "dotted_line"
            customerReceipt += "\n"
            customerReceipt += "feedline"
            customerReceipt += "\n"

            customerReceipt += "Rekening Tujuan"
            customerReceipt += "\n"
            customerReceipt += "feedline"
            customerReceipt += "\n"
            customerReceipt += order.items?.get(0)?.beneficiary?.name
            customerReceipt += "\n"
            customerReceipt += "feedline"
            customerReceipt += "\n"

            customerReceipt += order.items?.get(0)?.beneficiary?.code
            customerReceipt += " - "
            customerReceipt += order.items?.get(0)?.beneficiary?.accountNumber
            customerReceipt += "\n"
            customerReceipt += "feedline"
            customerReceipt += "\n"
            customerReceipt += "Pembayaran Berhasil"
            customerReceipt += "\n"

            if (order.agentFeeInfo?.amount != 0.0) {
                customerReceipt += "dotted_line"
                customerReceipt += "\n"
                customerReceipt += "Jumlah Pembayaran"
                repeat(2) { customerReceipt += " " }
                customerReceipt += Utils.formatAmount(order.items?.get(0)?.sellingPrice)
                customerReceipt += "\n"

                customerReceipt += "Biaya Layanan"
                repeat(6) { customerReceipt += " " }
                customerReceipt += Utils.formatAmount(order.agentFeeInfo?.amount)
            }

            customerReceipt += "\n"
            customerReceipt += "dotted_line"
            customerReceipt += "\n"

            customerReceipt += "left"
            customerReceipt += "\n"
            val total = order.items?.get(0)?.sellingPrice!! + order.agentFeeInfo?.amount!!
            customerReceipt += "Total Bayar"
            repeat(6) { customerReceipt += " " }
            customerReceipt += Utils.formatAmount(total)
            customerReceipt += "\n"
        }

        customerReceipt += "center"
        customerReceipt += "\n"

        customerReceipt += "dotted_line"
        customerReceipt += "\n"
        customerReceipt += binding.tvFooterMessage.text
        customerReceipt += "\n"
        customerReceipt += binding.tvFooterLink.text
        customerReceipt += "\n"
        customerReceipt += "dotted_line"
        customerReceipt += "\n"
        return customerReceipt
    }

}