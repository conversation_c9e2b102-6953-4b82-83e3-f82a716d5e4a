package com.bukuwarung.edc.payments.data.model

import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.lib.webview.data.PrivyCredentials
import com.bukuwarung.lib.webview.util.Constant
import com.bukuwarung.lib.webview.util.FileSizeLimits
import com.bukuwarung.lib.webview.util.UploadsValidations

data class PaymentConfigs(
    val disbursalMaxRetryAttempts: Int = 2,
    val visibleCategoryCount: Int = 3,
    val qrisBankChangeAllowedCount: Int? = null,
    val qrisBankChangeAllowedDays: Int? = null,
    val shouldShowQrisBankWarning: Boolean = false,
    val shouldCheckLuminosity: Boolean = false,
    val minRequiredLuminosity: Float = 0f,
    val photoCompressionSize: Float = 1536f,
    val minQrisAmountForRetry: Double? = 0.0,
    val qrisBatchDisbursementTime: String? = null,
    val qrisChargingAmount: String? = "",
    val qrisProcessingTimeFAQ: String? = null,
    val customCalendarMaxRange:Int? = PaymentConst.DEFAULT_FILTER_CALENDAR_MAX_RANGE,
    val hideBiayaAdminForQris: Boolean? = true,
    val fileSizeLimits: FileSizeLimits? = null,
    val performImageValidations: Boolean? = false,
    val uploadsValidations: UploadsValidations? = null,
    val imageQuality: Int? = null,
    val videoQuality: String? = null,
    val compressionQuality: String? = null,
    val receiverChannelWarnings: List<ReceiverWarning>? = null,
    val kycTierConfig: KycTierConfig? = null,
    val enableNameMatching: Boolean? = false,
    val enableKyb: Boolean? = false,
    val enableKybAlerts: Boolean? = false,
    val enablePaymentKyb: Boolean? = false,
    val isQrisDiscontinued: Boolean? = false,
    val livenessVendor: Constant.LivenessVendor? = Constant.LivenessVendor.VIDA,
    val bankingApi: String = BuildConfig.API_BASE_URL_BANKING,
    val riskApi: String = BuildConfig.API_BASE_URL_RISK,
    val janusApi: String = BuildConfig.API_BASE_URL_JANUS,
    val paymentApi: String = BuildConfig.API_BASE_URL_PAYMENT,
    val finProApi: String = BuildConfig.API_BASE_URL_FINPRO,
    val transactionsApi: String = BuildConfig.API_BASE_URL_TRANSACTIONS,
    val forgotPinUrl: String = BuildConfig.FORGOT_PIN_URL,
    val kycWebUrl: String = BuildConfig.KYC_WEB_URL,
    val kycDocsUrl: String = BuildConfig.KYC_DOCS_URL,
    val kybWebUrl: String = BuildConfig.KYB_WEB_URL,
    val accountVerificationUrl: String = BuildConfig.KYC_WEB_URL,
    val kycKybVerificationUrl: String = BuildConfig.KYC_KYB_WEB_URL,
    val miniAtmEdcOrderKyc: String = BuildConfig.MINIATM_EDC_ORDER_KYC,
    val qrisWebUrl: String = BuildConfig.QRIS_WEB_URL,
    val qrisWebUrlMatching: String = BuildConfig.QRIS_WEB_URL,
    val qrisWebUrlV3: String = BuildConfig.QRIS_WEB_URL,
    val qrisFormUrl: String = BuildConfig.QRIS_FORM_URL,
    val qrisFormUrlMatching: String = BuildConfig.QRIS_FORM_URL,
    val qrisFormUrlV3: String = BuildConfig.QRIS_FORM_URL,
    val lendingFormUrl: String = "${BuildConfig.MWEB_BASE_URL}/los-web/clf?page=Personal_Information",
    val bnplFormUrl: String = "${BuildConfig.MWEB_BASE_URL}/los-web/bnpl-forms",
    val ppobBnplFormUrl: String = "${BuildConfig.MWEB_BASE_URL}/los-web/bnpl/forms/ppob-bnpl?form=1",
    val supportUrls: PaymentSupportUrls = PaymentSupportUrls(),
    val kycTierInfoUrl: String = com.bukuwarung.edc.global.Constant.KYC_TIER_INFO_URL,
    val aboutKycUrl: String = com.bukuwarung.edc.global.Constant.ABOUT_ACCOUNT_VERIFICATION,
    val qrisBankUrl: String = BuildConfig.QRIS_BANK_URL,
    val appealFlowUrl: String = BuildConfig.APPEAL_FLOW_URL,
    val matchingInfoUrl: String = com.bukuwarung.edc.global.Constant.FAQ_MATCHING_INFO_URL,
    val faqUsedAccountUrl: String = com.bukuwarung.edc.global.Constant.FAQ_USED_ACCOUNT_BW_URL,
    val faqBlockedAccountUrl: String = com.bukuwarung.edc.global.Constant.FAQ_BLOCKED_ACCOUNT_BW_URL,
    val saldoBonusUrl: String = com.bukuwarung.edc.global.Constant.SALDO_BONUS_URL,
    val privyCredentials: PrivyCredentials? = null,
    val kycRedirectionData: HashMap<String, String>? = null,
    val qrisCoachMarks: List<QrisCoachMarks>? = null,
    val qrisFAQs: List<QrisFAQ>? = null,
    val historyFiltersNew: Filters? = null,
    val kybMandatoryFromDate: String? = null,
    val kybVerificationMaxDays: Int? = null,
    val mapConfig: MapConfig? = null,
    val paginationConfig: PaginationConfig? = null,
    val shouldEnableKybMapFlow: Boolean = true,
    val deactivatedQrisGroupCode: String? = null,
    val paymentOutPollingConfig: PaymentOutPollingConfig? = null,
    val qrisAlertConfig: QrisAlertConfig? = null,
    val merchantBanksUrl: String = "${BuildConfig.MWEB_BASE_URL}/mx-mweb/edc/landing/registration/connect-bank?from=android",
    val edcRegistrationUrl: String = "${BuildConfig.MWEB_BASE_URL}/mx-mweb/edc/landing/registration?step=1&readonly=true",

    )