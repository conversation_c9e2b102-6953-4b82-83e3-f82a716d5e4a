package com.bukuwarung.edc.payments.util

import Resource
import android.app.Activity
import android.content.DialogInterface
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.activityViewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.DialogEditPaymentReceiptBinding
import com.bukuwarung.edc.global.base.BaseDialogFragment
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.ui.core.PaymentViewModel
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.textHTML
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class EditPaymentReceiptDialog : BaseDialogFragment() {

    private var dialogEditPaymentReceiptBinding: DialogEditPaymentReceiptBinding? = null
    private val binding get() = dialogEditPaymentReceiptBinding!!

    private val viewModel: PaymentViewModel by activityViewModels()

    private lateinit var customerId: String
    private lateinit var orderId: String

    companion object {
        private const val SERVICE_FEE = "service_fee"
        private const val TOTAL_TRANSACTION = "total_transaction"
        private const val TRANSACTION_NOTE = "transaction_note"
        fun newInstance(totalTransaction: Double?, serviceFee: Double?, transactionNote: String?) = EditPaymentReceiptDialog().apply {
            arguments = Bundle().apply {
                putDouble(SERVICE_FEE, serviceFee.orNil)
                putDouble(TOTAL_TRANSACTION, totalTransaction.orNil)
                putString(TRANSACTION_NOTE, transactionNote)
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialogEditPaymentReceiptBinding = DialogEditPaymentReceiptBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            dialog?.window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            dialog?.window?.statusBarColor = requireContext().getColorCompat(R.color.colorPrimary)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel.orderDetail.observe(this) {
            populatePaymentReceipt(it)
            binding.etInputNominal.setText(Utils.formatAmount(it.data?.agentFeeInfo?.amount))
            customerId = it?.data?.customer?.customerId.orEmpty()
            orderId = it?.data?.orderId.orEmpty()
        }
        with(binding) {
            includeToolBar.ivHelp.hideView()
            includeToolBar.tvHelp.hideView()
            includeToolBar.tbPpob.navigationIcon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_arrow_back)
            includeToolBar.tbPpob.setNavigationOnClickListener {
                dismiss()
            }

                tvActualMoneyMessage1.hideView()
                includeToolBar.toolBarLabel.text = getString(R.string.change_bill_receipt, getString(R.string.payment_label_payment_out))

            btSaveEdit.setOnClickListener {
                val currency = getString(R.string.currency)
                val amountString = if (etInputNominal.text.toString() == currency) getString(R.string.zero_amount) else etInputNominal.text.toString()
                val amount = try {
                    (Utils.cleanBalance(amountString.substring(currency.length)))?.toDoubleOrNull()
                } catch (e: Exception) {
                    (Utils.cleanBalance(amountString.substring(2)))?.toDoubleOrNull()
                }
                viewModel.updateAgentFee(amount.orNil, etWriteNotes.text.toString(), customerId, orderId)
                dismiss()
            }
            etWriteNotes.textHTML(arguments?.getString(TRANSACTION_NOTE).orDefault("-"))
            etInputNominal.isCursorVisible = false
            etInputNominal.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    etInputNominal.post {
                        etInputNominal.setSelection(etInputNominal.length())
                        etInputNominal.isCursorVisible = true
                    }
                } else etInputNominal.isCursorVisible = false
            }
            etInputNominal.setOnClickListener {
                etInputNominal.setSelection(etInputNominal.length())
            }
            val amt = arguments?.getDouble(TOTAL_TRANSACTION) ?: 0.0
            etInputNominal.addTextChangedListener {
            setAmount(amt)
            }
            setAmount(amt)
        }
    }

    private fun setAmount(amt: Double) {
        with(binding) {
                val paymentOutAmount = amt + etInputNominal.getNumberValue()
                tvNominal.text = Utils.formatAmount(amt)
                tvTotalTransaction.text = Utils.formatAmount(paymentOutAmount)
//                orderInvoice.updatePaymentOutAmounts(
//                    totalAmount = paymentOutAmount,
//                    serviceFee = etInputNominal.getNumberValue().toDouble()
//                )
            }
    }

    private fun populatePaymentReceipt(state: Resource<OrderResponse>) {

            binding.orderInvoice.apply {
                makeInvoice(
                    orderResponse = state.data,
                    paymentType = state.data?.items?.getOrNull(0)?.sku,
                )
                showView()
            }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dialogEditPaymentReceiptBinding = null
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        val activity: Activity? = activity
        if (activity is EditDialogListener) (activity as EditDialogListener?)?.handleDialogClose(
            dialog
        )
    }

}

interface EditDialogListener {
    fun handleDialogClose(dialog: DialogInterface?)
}