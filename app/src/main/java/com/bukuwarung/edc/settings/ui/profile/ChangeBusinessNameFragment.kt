package com.bukuwarung.edc.settings.ui.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.bluetooth_printer.utils.showSnackbar
import com.bukuwarung.bluetooth_printer.utils.singleClick
import com.bukuwarung.edc.databinding.FragmentChangeBusinessNameBinding
import com.bukuwarung.edc.util.Utils.getDeviceSerialNumber
import com.bukuwarung.edc.util.Utils.setBusinessName
import com.bukuwarung.edc.util.setBusinessNameChanged
import com.bukuwarung.edc.util.setBusinessNameForSelectedSerialNumber
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ChangeBusinessNameFragment : Fragment() {

    val viewModel: ProfileViewModel by viewModels()
    private lateinit var binding: FragmentChangeBusinessNameBinding

    private val businessName by lazy {
        arguments?.getString(BUSINESS_NAME) ?: ""

    }
    private val address by lazy {
        arguments?.getString(ADDRESS) ?: ""
    }

    companion object {

        const val BUSINESS_NAME = "businessName"
        const val ADDRESS = "address"
        fun newInstance(businessName: String, address: String) =
            ChangeBusinessNameFragment().apply {
                arguments = Bundle().apply {
                    putString(BUSINESS_NAME, businessName)
                    putString(ADDRESS, address)
                }
            }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        viewModel.handleIntent(
            ProfileViewModel.ChangeBusinessNameIntent.ShowAddress(
                address
            )
        )
        viewModel.handleIntent(
            ProfileViewModel.ChangeBusinessNameIntent.ShowBusinessName(
                businessName
            )
        )
        binding = FragmentChangeBusinessNameBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.ivBack.singleClick {
            val manager = requireActivity().supportFragmentManager
            manager.popBackStack()
        }
        binding.etAddress.setText(address)

        binding.etBusinessName.doAfterTextChanged { text ->
            viewModel.handleIntent(
                ProfileViewModel.ChangeBusinessNameIntent.OnBusinessNameChange(
                    text.toString()
                )
            )
            binding.etBusinessName.setSelection(binding.etBusinessName.length())
        }

        binding.btnConfirm.singleClick {
            changeBusinessName(binding.etBusinessName.text.toString())
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.state.collect { uiState ->
                if (uiState.businessName == uiState.newBusinessName) {
                    binding.etBusinessName.setText(uiState.newBusinessName)
                }

                binding.btnConfirm.isEnabled = uiState.isButtonEnabled
                binding.tvErrorMessage.visibility = if (uiState.isError) View.VISIBLE else View.GONE
                binding.tvErrorMessage.text = uiState.errorMessage

                binding.progressLoading.visibility =
                    if (uiState.isLoading) View.VISIBLE else View.GONE

                if (uiState.success) {
                    setBusinessName(uiState.newBusinessName)
                    setBusinessNameForSelectedSerialNumber(
                        getDeviceSerialNumber(),
                        uiState.newBusinessName
                    )
                    setBusinessNameChanged(true)
                    requireContext().showSnackbar(
                        binding.tvName,
                        "Berhasil ubah nama toko"
                    )
                    requireActivity().supportFragmentManager.popBackStack()
                }
            }
        }
    }

    private fun changeBusinessName(
        newName: String
    ) {
        viewModel.handleIntent(ProfileViewModel.ChangeBusinessNameIntent.ValidateName(newName))
    }
}