package com.bukuwarung.edc.settings.ui.setting

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.setPadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity.Companion.CARD_READER
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity.Companion.PRINTER
import com.bukuwarung.bluetooth_printer.utils.PermissionConst
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.activation.ui.EdcActivationFragment
import com.bukuwarung.edc.card.activation.ui.EdcActivationFragment.EDCActivationFragmentType
import com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity
import com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity.Companion.HISTORY_TYPE
import com.bukuwarung.edc.card.cashwithdrawal.ui.activity.SettlementAccountActivity
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.databinding.ActivitySettingBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants.PAIRED_TO_SAKU_CLICKED
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.order.orderhistory.enums.HistoryType
import com.bukuwarung.edc.payments.constant.PaymentAnalyticsConst
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.printer.EDCPrint
import com.bukuwarung.edc.printer.util.PrintUtil
import com.bukuwarung.edc.settings.ui.profile.ProfileActivity
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.openActivityForResult
import com.bukuwarung.edc.util.setDrawableRightListener
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.homepage.ui.home.bottomsheet.KycKybBottomSheet
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.constant.PinType
import com.bukuwarung.edc.payments.data.model.KycTier
import com.bukuwarung.edc.payments.ui.core.NewPaymentPinActivity
import com.bukuwarung.edc.payments.ui.core.PaymentPinFixedTerminalActivity
import com.bukuwarung.edc.payments.ui.core.PinChangeStatus
import com.bukuwarung.edc.util.*
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.google.android.material.snackbar.Snackbar
import com.tiktok.TikTokBusinessSdk
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class SettingActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySettingBinding

    private val viewModel: SettingViewModel by viewModels()

    private val startConnectSakuDeviceResultContract =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == RESULT_OK) {
                val isActivation = result.data?.getBooleanExtra("IS_ACTIVATED", false) == true
                if (isActivation) {
                    redirectToHomePageAfterActivation()
                }
            }
        }

    private var hasPin = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingBinding.inflate(layoutInflater)

        setContentView(binding.root)

        setUpToolbar()

        binding.apply {
            ("Version: " + BuildConfig.VERSION_NAME).also { tvAppVersionCode.text = it }
            ("Version Code: " + BuildConfig.VERSION_CODE).also { tvAppVersionName.text = it }
            tvPhoneNumber.visibility = BuildConfig.DEBUG.asVisibility()
            tvPhoneNumber.text = "Phone number: ${Utils.getPhoneNumber()}"
        }

        setUpClickListeners()
        if (!Utils.isCardReader()) {
            binding.tvPaymentSection.setText("STRUK PEMBAYARAN")
            binding.tvMiniedc.root.hideView()
            binding.tvPrinter.root.hideView()
            binding.tvActivate.root.hideView()
            binding.tvTestPrinter.showView()
            binding.tvSound.showView()
        }else{
            binding.tvPaymentSection.setText("PERANGKAT")
            binding.tvMiniedc.root.showView()
            binding.tvPrinter.root.showView()
            binding.tvActivate.leftText.text = getString(R.string.active_device_list)
            binding.tvActivate.root.showView()
            binding.tvTestPrinter.hideView()
            binding.tvSound.hideView()
        }
        if (Utils.isAtmPro()) {
            binding.tvChangePin.hideView()
            binding.vwDividerChangePin.root.hideView()
            binding.tvPaymentSection.text = getString(R.string.perangkat)
            binding.tvActivate.leftText.text = getString(R.string.active_device_list)
            binding.tvActivate.root.showView()
        }
        checkFragmentState()
        binding.grpPin.hideView()
        if (Utils.isAtmPro().not()) viewModel.checkPinLength()
        subscribeState()
        binding.tvChangePin.singleClick {
            val kycTier = Utils.getKycTierFromToken()
            if (kycTier == KycTier.NON_KYC) {
                checkIfFixedTerminal(true) {
                    KycKybBottomSheet.createInstance()
                        .show(supportFragmentManager, KycKybBottomSheet.getClassTag())
                }
            } else {
                when {
                    hasPin.isFalse -> {
                        openNewPaymentPinScreen(PinType.PIN_CREATE.toString())
                    }

                    else -> {
                        viewModel.checkPinChangeRequest()
                    }
                }
            }
        }
    }

    private fun subscribeState() {

        viewModel.isConfirmOrderFirstTime.observe(this) {
            if (it) {
                supportFragmentManager.beginTransaction()
                    .replace(
                        R.id.containerOrderEdc,
                        EdcActivationFragment.newInstance(EDCActivationFragmentType.MiniAtmPro)
                    )
                    .addToBackStack(null)
                    .commit()
            } else {
                openActivity(CardTransactionHistoryActivity::class.java) {
                    putString(HISTORY_TYPE, HistoryType.order.name)
                }
            }
        }

        viewModel.pinChangeResponse.observe(this){
            when(it?.content?.firstOrNull()?.status.orEmpty()){
                PinChangeStatus.PENDING_OTP_VERIFICATION.name -> {
                    checkIfFixedTerminal(false) { openNewPaymentPinScreen(PinType.PIN_FORGOT_ENTER_OTP_STEP.toString()) }
                }
                PinChangeStatus.PENDING_PIN_INPUT.name -> {
                    checkIfFixedTerminal(false) { openNewPaymentPinScreen(PinType.PIN_FORGOT_CREATE_PIN_STEP.toString()) }
                }
                PinChangeStatus.PENDING_MANUAL_VERIFICATION.name -> {
                    openActivity(WebviewActivity::class.java){
                        putString(ClassConstants.WEBVIEW_URL, PaymentRemoteConfig.getPaymentConfigs().forgotPinUrl+"?landing=BUKUAGEN")
                        putBoolean(ClassConstants.HIDE_TOOLBAR, true)
                    }
                }
                PinChangeStatus.REJECTED.name -> {
                    checkIfFixedTerminal(false){
                        openActivity(WebviewActivity::class.java){
                            putString(ClassConstants.WEBVIEW_URL, PaymentRemoteConfig.getPaymentConfigs().forgotPinUrl+"?landing=BUKUAGEN")
                            putBoolean(ClassConstants.HIDE_TOOLBAR, true)
                        }
                    }
                }
                PinChangeStatus.VERIFIED.name -> {
                    if (sharedPreferences.get("pin_change_verified", false)){
                        openNewPaymentPinScreen(PinType.PIN_UPDATE.toString())
                    } else {
                        sharedPreferences.put("pin_change_verified", true)
                        openActivity(WebviewActivity::class.java){
                            putString(ClassConstants.WEBVIEW_URL, PaymentRemoteConfig.getPaymentConfigs().forgotPinUrl+"?landing=BUKUAGEN")
                            putBoolean(ClassConstants.HIDE_TOOLBAR, true)
                        }
                    }
                }
                PinChangeStatus.VOID.name, "" -> {
                    openNewPaymentPinScreen(PinType.PIN_UPDATE.toString())
                }
                else -> {
                    openNewPaymentPinScreen(PinType.PIN_UPDATE.toString())
                }

            }
        }
        viewModel.hasPin.observe(this) { hasPin ->
            <EMAIL> = hasPin
            binding.grpPin.showView()
            binding.tvChangePin.text = getString(if (hasPin) R.string.change_saldo_pin else R.string.set_saldo_pin)
        }
    }

    private fun setUpToolbar() {
        with(binding.tbSettings) {
            navigationIcon =
                <EMAIL>(R.drawable.ic_back)
            setNavigationOnClickListener {
                Utils.hideKeyboard(this@SettingActivity)
                finish()
            }
        }
    }

    private fun setUpClickListeners() {
        binding.apply {
            tvProfile.singleClick {
                if (Utils.getUserId().isNotEmpty()) {
                    openActivity(ProfileActivity::class.java)
                } else {
                    Toast.makeText(this@SettingActivity, "Empty Device Details", Toast.LENGTH_LONG)
                        .show()
                }
            }

            tvCashWithdrawalAccount.singleClick {
                openActivity(SettlementAccountActivity::class.java)
            }

            tvPrinter.deviceItem.singleClick {
                openActivity(SetupBluetoothDeviceActivity::class.java) {
                    putString(
                        SetupBluetoothDeviceActivity.DEVICE_TYPE,
                        SetupBluetoothDeviceActivity.PRINTER
                    )
                }
            }

            tvTestPrinter.setOnClickListener { view ->
                //refactor:create single interface for printer
                if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX) {
                    var textToPrint = ""
                    textToPrint += "dotted_line"
                    textToPrint += "\n"
                    textToPrint += "Bukuwarung EDC App Test Print"
                    textToPrint += "dotted_line"
                    EDCPrint.startPrinting(textToPrint)
                    PrintUtil.printUtil.apply {
                        setToast(getStatusCode(), view)
                        val map = HashMap<String, String>()
                        map["status"] = getStatus().toString()
                        Analytics.trackEvent(SettingsAnalytics.TEST_PRINT_COMPLETED, map)
                    }
                } else if(Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
                    viewModel.printTestSlip()
                }
            }

            tvMiniedc.deviceItem.singleClick {
                val device = Utils.getUserRegisteredDevices()
                val deviceSerialNumberList = device.map { it.serialNumber }
                if (Utils.getBooleanConfig("card_reader_iniatiated") && CardReaderHelper.getInstance()
                        ?.isDeviceConnected() == true
                ) {
                    object : Thread() {
                        override fun run() {
                            CardReaderHelper.getInstance()?.disconnectFromDevice()
                            runOnUiThread {
                                openActivityForResult(
                                    SetupBluetoothDeviceActivity::class.java,
                                    startConnectSakuDeviceResultContract
                                ) {
                                    putString(
                                        SetupBluetoothDeviceActivity.DEVICE_TYPE,
                                        SetupBluetoothDeviceActivity.CARD_READER
                                    )
                                    putStringArrayList(
                                        SetupBluetoothDeviceActivity.DEVICE_SN_LIST,
                                        ArrayList(deviceSerialNumberList)
                                    )
                                }
                            }
                        }
                    }.start()
                } else {
                    if (!BluetoothDevices.hasPairedCardReader()) {
                        Analytics.trackEventMobile(PAIRED_TO_SAKU_CLICKED)
                    }
                    openActivityForResult(
                        SetupBluetoothDeviceActivity::class.java,
                        startConnectSakuDeviceResultContract
                    ) {
                        putString(
                            SetupBluetoothDeviceActivity.DEVICE_TYPE,
                            SetupBluetoothDeviceActivity.CARD_READER
                        )
                        putStringArrayList(
                            SetupBluetoothDeviceActivity.DEVICE_SN_LIST,
                            ArrayList(deviceSerialNumberList)
                        )
                    }
                }
            }

            tvPaymentAdminFee.singleClick {

            }

            tvActivate.root.singleClick {
                // have not done confirm order for the 1st time
                viewModel.getEdcOrderHistory()
            }

            tvLogout.singleClick {
                LogoutDialog(this@SettingActivity, onDialogClose = {
                    finishAffinity()
                    TikTokBusinessSdk.logout()
                }).show()
            }
            tvSound.singleClick {
                openActivity(SoundSettingsActivity::class.java)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (Utils.isCardReader()) {
            renderBtDeviceConnectionState()
        }
    }

    private fun setToast(code: Int?, view: View) {
        val snackBar = Snackbar.make(view, "", Snackbar.LENGTH_LONG)
        val customSnackView: View = layoutInflater.inflate(R.layout.custom_toast, null)

        customSnackView.findViewById<AppCompatTextView>(R.id.tv_toast_message).text =
            PrintUtil.printUtil.findStatusText(code)
        customSnackView.findViewById<AppCompatImageView>(R.id.iv_toast_image)
            .setImageDrawable(PrintUtil.printUtil.findToastImage(code))
        customSnackView.findViewById<AppCompatTextView>(R.id.tv_toast_message)
            .setTextColor(PrintUtil.printUtil.findTextColor(code))


        val snackbarLayout = snackBar.view as Snackbar.SnackbarLayout
        snackbarLayout.setPadding(0)

        val params = snackbarLayout.layoutParams as FrameLayout.LayoutParams
        params.apply {
            setMargins(50, 135, 50, 12)
        }

        params.gravity = Gravity.TOP
        snackbarLayout.background = resources.getDrawable(R.drawable.bg_toast_printer, null)
        snackbarLayout.layoutParams = params
        snackBar.setBackgroundTintList(PrintUtil.printUtil.findTintColor(code))

        snackbarLayout.addView(customSnackView, 0)
        snackBar.show()
    }

    private fun checkFragmentState() {
        supportFragmentManager.registerFragmentLifecycleCallbacks(object :
            FragmentManager.FragmentLifecycleCallbacks() {

            override fun onFragmentAttached(fm: FragmentManager, f: Fragment, context: Context) {
                super.onFragmentAttached(fm, f, context)
                if (f is EdcActivationFragment) {
                    binding.clSetting.hideView()
                    binding.containerOrderEdc.showView()
                }
            }

            override fun onFragmentDetached(fm: FragmentManager, f: Fragment) {
                super.onFragmentDetached(fm, f)
                if (f is EdcActivationFragment) {
                    binding.clSetting.showView()
                    binding.containerOrderEdc.hideView()
                }
            }

        }, false)
    }

    private fun renderBtDeviceConnectionState() {
        binding.tvMiniedc.leftText.text = getString(R.string.conncet_edc_miniatmpro)
        binding.tvMiniedc.rightText.text = getString(R.string.not_yet_connected)
        if (Utils.isAtmPro()) binding.tvMiniedc.rightText.setTextColor(getColor(R.color.colorPrimary))
        binding.tvPrinter.leftText.text = getString(R.string.connect_to_printer)
        binding.tvPrinter.rightText.text = getString(R.string.not_yet_connected)
        if (Utils.isAtmPro()) binding.tvPrinter.rightText.setTextColor(getColor(R.color.colorPrimary))
        if (PermissionUtils.checkPermission(PermissionConst.BLUETOOTH_PERMISSION)) {
            //checking when user paired bluetooth in phone setting
            viewModel.getPairedBtDevice(CARD_READER)?.let {
                binding.tvMiniedc.rightText.text = getString(R.string.connected)
                binding.tvMiniedc.rightTextSubtitle.text = it.name
                saveDeviceInfoAfterDeviceIsPaired(it.name)
                binding.tvMiniedc.rightTextSubtitle.showView()
            } ?: let {
                binding.tvMiniedc.rightTextSubtitle.hideView()
            }

            viewModel.getPairedBtDevice(PRINTER)?.let {
                binding.tvPrinter.rightText.text = getString(R.string.connected)
                binding.tvPrinter.rightTextSubtitle.text = it.name
                binding.tvPrinter.rightTextSubtitle.showView()
            } ?: let {
                binding.tvPrinter.rightTextSubtitle.hideView()
            }
        } else {
            //Checking pref-saved paired Bluetooth
            if (BluetoothDevices.hasPairedCardReader()) {
                binding.tvMiniedc.rightText.text = getString(R.string.connected)
                PrinterPrefManager(this).installedCardReaders?.firstOrNull()?.let {
                    binding.tvMiniedc.rightTextSubtitle.text = it.name
                    saveDeviceInfoAfterDeviceIsPaired(it.name)
                    binding.tvMiniedc.rightTextSubtitle.showView()
                }
            }
            if (BluetoothDevices.hasPairedPrinter()) {
                binding.tvPrinter.rightText.text = getString(R.string.connected)
                PrinterPrefManager(this).installedPrinters?.firstOrNull()?.let {
                    binding.tvPrinter.rightTextSubtitle.text = it.name
                    binding.tvPrinter.rightTextSubtitle.showView()
                }
            }
        }
    }

    private fun redirectToHomePageAfterActivation() {
        val intent = Intent(this, HomePageActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        intent.putExtra("IS_ACTIVATED", false)
        startActivity(intent)
        finish()
    }

    private fun saveDeviceInfoAfterDeviceIsPaired(name: String) {
        val devices = Utils.getUserRegisteredDevices()
        val deviceInfo = Utils.extractPaymentAccountIdTerminalIdSerialNumber(name, devices)
        bwLog("--->info", deviceInfo.toString())
        sharedPreferences.put("t_id", deviceInfo?.terminalId)
        sharedPreferences.put("serial_number", deviceInfo?.serialNumber)
        EncryptedPreferencesHelper.put("payment_account_id", deviceInfo?.paymentAccountId.orEmpty())
    }

    private fun openNewPaymentPinScreen(useCase: String){
        openActivityForResult(NewPaymentPinActivity::class.java, startNewPaymentPinActivityForResult) {
            putString(NewPaymentPinActivity.USECASE, useCase)
        }
    }

    private val startNewPaymentPinActivityForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                hasPin = true
                binding.tvChangePin.text = getString(R.string.change_saldo_pin)
            }
        }

    private fun checkIfFixedTerminal(isCreatePinFlow: Boolean, action: () -> Unit){
        if (Utils.isFixedTerminal()){
            openActivity(PaymentPinFixedTerminalActivity::class.java){
                putString(PaymentPinFixedTerminalActivity.ENTRY_POINT, if (isCreatePinFlow)PaymentPinFixedTerminalActivity.CREATE_PIN else PaymentPinFixedTerminalActivity.UPDATE_PIN)
            }
        } else {
            action()
        }
    }
}