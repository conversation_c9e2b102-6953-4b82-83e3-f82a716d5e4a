package com.bukuwarung.edc.verifyotp.data.datasource

import com.bukuwarung.edc.payments.data.model.response.OtpResponse
import com.bukuwarung.edc.verifyotp.data.model.VerifyOtpRequest
import com.bukuwarung.edc.verifyotp.data.model.VerifyOtpResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

interface VerifyOtpDataSource {

    @POST("api/v2/auth/login")
    suspend fun verifyOtp(
        @Header("x-ops-token") otpToken: String,
        @Body request: VerifyOtpRequest
    ): Response<VerifyOtpResponse>

    @POST("api/v2/auth/otp/verify")
    suspend fun verifyGenericOtp(
        @Header("x-ops-token") otpToken: String = "",
        @Body request: VerifyOtpRequest
    ): Response<OtpResponse>
}